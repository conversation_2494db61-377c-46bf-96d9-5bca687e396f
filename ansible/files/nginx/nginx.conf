# The user account used by the worker processes. If following along with our guide,
# it's recommened to set this to your username, but only when running a single user access server.
# https://spinupwp.com/hosting-wordpress-yourself-nginx-php-mysql/
user deployer;

# Set to number of CPU cores, auto will try to autodetect.
worker_processes auto;

# Maximum open file descriptors per process. Should be greater than worker_connections.
worker_rlimit_nofile 8192;

# File that stores the process ID. Rarely needs changing.
pid /run/nginx.pid;

events {
	# Set the maximum number of connection each worker process can open. Anything higher than this
	# will require Unix optimisations.
	worker_connections 8000;

	# Accept all new connections as they're opened.
	multi_accept on;
}

http {
	# HTTP
	include global/http.conf;

	# MIME Types
	include global/mime-types.conf;
	default_type application/octet-stream;

	# Limits & Timeouts
	include global/limits.conf;

	# Some WP plugins that push large amounts of data via cookies
	# can cause 500 HTTP errors if these values aren't increased.
	fastcgi_buffers 16 16k;
	fastcgi_buffer_size 32k;

	# Default Logs
	error_log /var/log/nginx/error.log warn;
	access_log /var/log/nginx/access.log;

	# Gzip
	include global/gzip.conf;

	# exposes configured php pool on $upstream variable
	include global/php-pool.conf;

	# SSL
	include global/ssl.conf;

	# Modules
	include /etc/nginx/conf.d/*.conf;

	# Sites
	include /etc/nginx/sites-enabled/*;
}