# Expose Docker container via Nginx
server {
	listen [::]:443 ssl http2;
	listen 443 ssl http2;

	# Server name to listen for
	server_name [[DOMAIN]] www.[[DOMAIN]];

	# Paths to certificate files.
	ssl_certificate /etc/cf/[[DOMAIN]]/crt.pem;
	ssl_certificate_key /etc/cf/[[DOMAIN]]/key.pem;


	# Overrides logs defined in nginx.conf, allows per site logs.
	access_log /home/<USER>/sites/[[DOMAIN]]/logs/access.log;
	error_log /home/<USER>/sites/[[DOMAIN]]/logs/error.log;

	# Global defaults
	include global/defaults.conf;

	# Fastcgi cache rules
	include global/fastcgi-cache.conf;

	location / {
		proxy_pass http://127.0.0.1:[[PORT]];

		proxy_http_version 1.1;

		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';

		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
	}
}

# Redirect http to https
server {
	listen 80;
	listen [::]:80;
	server_name [[DOMAIN]] www.[[DOMAIN]];

	return 301 https://[[DOMAIN]]$request_uri;
}