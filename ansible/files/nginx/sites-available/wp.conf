# Define path to cache and memory zone. The memory zone should be unique.
# keys_zone=[[DOMAIN]]:100m creates the memory zone and sets the maximum size in MBs.
# inactive=60m will remove cached items that haven't been accessed for 60 minutes or more.
# levels=1:2 defines the directory structure for the cache files.
fastcgi_cache_path /home/<USER>/sites/[[DOMAIN]]/cache levels=1:2 keys_zone=[[DOMAIN]]:100m inactive=60m;

server {
	# Ports to listen on
	listen [::]:443 ssl http2;
	listen 443 ssl http2;


	# Server name to listen for
	server_name [[DOMAIN]] www.[[DOMAIN]];

	# Path to document root
	root /home/<USER>/sites/[[DOMAIN]]/public;

	# # Paths to certificate files.
	ssl_certificate /etc/cf/[[DOMAIN]]/crt.pem;
	ssl_certificate_key /etc/cf/[[DOMAIN]]/key.pem;

	# File to be used as index
	index index.php;

	# Overrides logs defined in nginx.conf, allows per site logs.
	access_log /home/<USER>/sites/[[DOMAIN]]/logs/access.log;
	error_log /home/<USER>/sites/[[DOMAIN]]/logs/error.log;

	# Global defaults
	include global/defaults.conf;

	# Static content caching
	include global/static-files.conf;

	# Fastcgi cache rules
	include global/fastcgi-cache.conf;

	location / {
		try_files $uri $uri/ /index.php?$args;
	}

	location ~ \.php$ {
		try_files $uri =404;
		include global/fastcgi-params.conf;

		# Use the php pool defined in the upstream variable.
		# See global/php-pool.conf for definition.
		fastcgi_pass $upstream;

		# Skip cache based on rules in global/server/fastcgi-cache.conf.
		fastcgi_cache_bypass $skip_cache;
		fastcgi_no_cache $skip_cache;

		# Define memory zone for caching. Should match key_zone in fastcgi_cache_path above.
		fastcgi_cache [[DOMAIN]];

		# Define caching time.
		fastcgi_cache_valid 60m;
	}
}

# Redirect http to https
server {
	listen 80;
	listen [::]:80;
	server_name [[DOMAIN]] www.[[DOMAIN]];

	return 301 https://[[DOMAIN]]$request_uri;
}