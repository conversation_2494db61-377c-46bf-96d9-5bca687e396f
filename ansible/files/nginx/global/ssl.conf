# Generic SSL enhancements. Use https://www.ssllabs.com/ssltest/ to test
# and recommend further improvements.

# Define the size of the SSL session cache in MBs.
ssl_session_cache shared:SSL:10m;
# Define the time in minutes to cache SSL sessions.
ssl_session_timeout 1440m;
ssl_session_tickets off;
# Don't use outdated SSLv3 protocol. Protects against BEAST and POODLE attacks.
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
# Use secure ciphers
ssl_ciphers "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384";
ssl_dhparam /etc/nginx/dhparam.pem;
# Use HTTPS exclusively for 1 year, uncomment one. Second line applies to subdomains.
add_header Strict-Transport-Security "max-age=31536000;";
# add_header Strict-Transport-Security "max-age=31536000; includeSubdomains;";
