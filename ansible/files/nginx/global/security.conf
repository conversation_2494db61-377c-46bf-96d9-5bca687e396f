# Generic security enhancements. Use https://securityheaders.io to test
# and recommend further improvements.

# Hide Nginx version in error messages and reponse headers.
server_tokens off;

# Don't allow pages to be rendered in an iframe on external domains.
add_header X-Frame-Options "SAMEORIGIN" always;

# MIME sniffing prevention
add_header X-Content-Type-Options "nosniff" always;

# Enable cross-site scripting filter in supported browsers.
add_header X-Xss-Protection "1; mode=block" always;

# Whitelist sources which are allowed to load assets (JS, CSS, etc). The following will block
# only none HTTPS assets, but check out https://scotthelme.co.uk/content-security-policy-an-introduction/
# for an in-depth guide on creating a more restrictive policy.
# add_header Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';" always;