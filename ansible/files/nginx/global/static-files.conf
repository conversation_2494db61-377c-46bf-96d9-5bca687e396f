# Don't cache appcache, document html and data.
location ~* \.(?:manifest|appcache|html?|xml|json)$ {
	expires 0;
}

# Cache RSS and Atom feeds.
location ~* \.(?:rss|atom)$ {
	expires 1h;
}

# Caches images, icons, video, audio, HTC, etc.
location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|mp4|ogg|ogv|webm|htc)$ {
	expires 1y;
	access_log off;
}

# Cache svgz files, but don't compress them.
location ~* \.svgz$ {
	expires 1y;
	access_log off;
	gzip off;
}

# Cache CSS and JavaScript.
location ~* \.(?:css|js)$ {
	expires 1y;
	access_log off;
}

# Cache WebFonts.
location ~* \.(?:ttf|ttc|otf|eot|woff|woff2)$ {
	expires 1y;
	access_log off;
	add_header Access-Control-Allow-Origin *;
}

# Don't record access/error logs for robots.txt.
location = /robots.txt {
	try_files $uri $uri/ /index.php?$args;
	access_log off;
	log_not_found off;
}