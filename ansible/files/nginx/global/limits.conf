# How long each connection should stay open for.
keepalive_timeout 15;

# Timeout for reading client request body.
client_body_timeout 30;

# Timeout for reading client request header.
client_header_timeout 30;

# Timeout for transmitting reponse to client.
send_timeout 30;

# Set the maximum allowed size of client request body. This should be set
# to the value of files sizes you wish to upload to the WordPress Media Library.
# You may also need to change the values `upload_max_filesize` and `post_max_size` within
# your php.ini for the changes to apply.
client_max_body_size 512m;