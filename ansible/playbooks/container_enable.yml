---
- name: Deploy Docker Container
  hosts: all
  gather_facts: false
  remote_user: '{{ user }}'
  # no_log: true
  vars:
    ansible_user: '{{ user }}'
    # ansible_become_pass: '{{ password }}'
    ansible_port: '{{ ssh_port }}'
  vars_files:
    - ../configs/user.yml

  roles:
    - container

  post_tasks:
    - name: Wait for Container to be ready
      ansible.builtin.pause:
        seconds: 15

    - name: Verify container is running
      ansible.builtin.command: docker ps -q --filter "name={{ container_name }}"
      register: docker_ps_output
      changed_when: false

    - name: Fail if container is not running
      ansible.builtin.fail:
        msg: 'Container {{ container_name }} is not running.'
      when: docker_ps_output.stdout == ""

    - name: Verify container site is accessible
      ansible.builtin.uri:
        url: 'http://{{ domain_name }}'
        method: GET
        status_code: [200, 301, 302]
        timeout: 30
        validate_certs: true
      register: container_response
      until: container_response.status in [200, 301, 302]
      retries: 5
      delay: 5
