---
- name: Ensure MariaDB service is running and enabled
  ansible.builtin.systemd:
    name: mariadb
    state: started
    enabled: true

- name: Wait for MariaDB Unix socket to be available
  ansible.builtin.wait_for:
    path: '{{ mysql_unix_socket_path }}'
    state: present
    delay: 5
    timeout: 60

- name: Check if root password is already set
  community.mysql.mysql_info:
    login_unix_socket: '{{ mysql_unix_socket_path }}'
    login_user: root
    login_password: '{{ mysql_root_password }}'
  register: mysql_root_check
  ignore_errors: true

- name: Secure MariaDB installation (set root password)
  community.mysql.mysql_user:
    name: root
    password: '{{ mysql_root_password }}'
    priv: '*.*:ALL,GRANT'
    host: localhost
    state: present
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  when: mysql_root_check is failed
