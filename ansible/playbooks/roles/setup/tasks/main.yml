---
- name: Set timezone
  community.general.timezone:
    name: Africa/Lagos

- name: Set locale
  community.general.locale_gen:
    name: en_US.UTF-8
    state: present

- name: Configure locale environment
  ansible.builtin.lineinfile:
    path: /etc/default/locale
    regexp: '^{{ item.key }}='
    line: '{{ item.key }}={{ item.value }}'
    state: present
    create: true
    mode: '0644'
  loop:
    - { key: 'LANG', value: 'en_US.UTF-8' }
    - { key: 'LC_ALL', value: 'en_US.UTF-8' }

- name: Set up user
  ansible.builtin.user:
    name: '{{ user }}'
    state: present
    create_home: true
    shell: /bin/bash
    password: '!'
    groups: sudo
    append: true

- name: Allow passwordless sudo for user
  ansible.builtin.copy:
    dest: '/etc/sudoers.d/{{ user }}'
    content: "{{ user }} ALL=(ALL) NOPASSWD:ALL\n"
    owner: root
    group: root
    mode: '0440'
    validate: 'visudo -cf %s'

- name: Set up SSH directory and copy authorized keys
  block:
    - name: Ensure .ssh directory exists with correct permissions
      ansible.builtin.file:
        path: '/home/<USER>/.ssh'
        state: directory
        owner: '{{ user }}'
        group: '{{ user }}'
        mode: '0700'

    - name: Copy authorized keys from root to user
      ansible.builtin.copy:
        src: /root/.ssh/authorized_keys
        dest: /home/<USER>/.ssh/authorized_keys
        owner: '{{ user }}'
        group: '{{ user }}'
        mode: '0600'
        remote_src: true
        force: false

- name: Configure SSH security settings
  ansible.builtin.lineinfile:
    path: /etc/ssh/sshd_config
    regexp: '{{ item.regexp }}'
    line: '{{ item.line }}'
    validate: '/usr/sbin/sshd -t -f %s'
  loop:
    - { regexp: '^#?PermitRootLogin\s+true$', line: 'PermitRootLogin no' }
    - { regexp: '^#?PasswordAuthentication\s+true$', line: 'PasswordAuthentication no' }
    - { regexp: '^#?PermitEmptyPasswords\s+true$', line: 'PermitEmptyPasswords no' }
    - { regexp: '^#?X11Forwarding\s+true$', line: 'X11Forwarding no' }
    - { regexp: '^#?Port\s+\d+$', line: 'Port {{ ssh_port }}' }
  notify: Restart SSH

- name: Flush handlers
  ansible.builtin.meta: flush_handlers
