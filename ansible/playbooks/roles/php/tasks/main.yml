---
- name: Add Sury.org PHP repository GPG key
  ansible.builtin.apt_key:
    url: https://packages.sury.org/php/apt.gpg
    state: present
    keyring: /usr/share/keyrings/sury-php-archive-keyring.gpg

- name: Add Sury.org PHP repository
  ansible.builtin.apt_repository:
    repo: deb [signed-by=/usr/share/keyrings/sury-php-archive-keyring.gpg] https://packages.sury.org/php/ bookworm main
    state: present
    filename: sury-php

- name: Update apt cache after adding Sury.org repo
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 3600

- name: Define PHP versions
  ansible.builtin.set_fact:
    php_versions:
      - '8.2'
      - '8.3'
      - '8.4'

- name: Define PHP extensions
  ansible.builtin.set_fact:
    php_extensions:
      - fpm
      - mysql
      - curl
      - gd
      - mbstring
      - xml
      - zip
      - soap
      - intl
      - cli
      - opcache
      - imagick

- name: Install PHP versions with extensions
  ansible.builtin.apt:
    name: 'php{{ item.0 }}-{{ item.1 }}'
    state: present
  loop: '{{ php_versions | product(php_extensions) | list }}'

- name: Configure www.conf for each PHP version
  ansible.builtin.lineinfile:
    path: '/etc/php/{{ item.0 }}/fpm/pool.d/www.conf'
    regexp: "^;?\\s*{{ item.1.key }}\\s*="
    line: '{{ item.1.key }} = {{ item.1.value }}'
    backup: true
  loop: '{{ php_versions | product(www_conf_settings) | list }}'
  vars:
    www_conf_settings:
      - { key: 'user', value: '{{ user }}' }
      - { key: 'group', value: '{{ user }}' }
      - { key: 'listen.owner', value: '{{ user }}' }
      - { key: 'listen.group', value: '{{ user }}' }
  notify: Restart PHP-FPM

- name: Configure php.ini for each PHP version
  ansible.builtin.lineinfile:
    path: '/etc/php/{{ item.0 }}/fpm/php.ini'
    regexp: "^;?\\s*{{ item.1.key }}\\s*="
    line: '{{ item.1.key }} = {{ item.1.value }}'
    backup: true
  loop: '{{ php_versions | product(php_ini_settings) | list }}'
  vars:
    php_ini_settings:
      - { key: 'upload_max_filesize', value: '512M' }
      - { key: 'post_max_size', value: '512M' }
      - { key: 'opcache.enable_file_override', value: '1' }
      - { key: 'memory_limit', value: '1024M' }
      - { key: 'max_execution_time', value: '300' }
  notify: Restart PHP-FPM
