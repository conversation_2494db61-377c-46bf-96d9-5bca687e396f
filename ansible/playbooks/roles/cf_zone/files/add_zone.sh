#!/bin/bash

# Cloudflare Zone Management Script
# Usage: ./add_zone.sh <domain> <api_token> [account_id]
# Returns: zone_id, zone_name, nameservers (one per line)

set -euo pipefail

readonly DOMAIN="$1"
readonly API_TOKEN="$2"
readonly ACCOUNT_ID="${3:-}"

if [[ -z "$DOMAIN" || -z "$API_TOKEN" ]]; then
    echo "Usage: $0 <domain> <api_token> [account_id]" >&2
    exit 1
fi

readonly API_URL="https://api.cloudflare.com/client/v4"

# Make authenticated API requests
api_request() {
    local method="$1"
    local endpoint="$2"
    local data="${3:-}"

    local curl_args=(
        -s -f
        -X "$method"
        -H "Authorization: Bearer $API_TOKEN"
        -H "Content-Type: application/json"
        -w "%{http_code}"
    )

    if [[ -n "$data" ]]; then
        curl_args+=(--data "$data")
    fi

    curl "${curl_args[@]}" "${API_URL}${endpoint}"
}

# Get account ID if not provided
get_account_id() {
    if [[ -n "$ACCOUNT_ID" ]]; then
        echo "$ACCOUNT_ID"
        return
    fi

    local response
    if ! response=$(api_request "GET" "/accounts" 2>/dev/null); then
        exit 1
    fi

    local http_code="${response: -3}"
    local body="${response%???}"

    if [[ "$http_code" != "200" ]]; then
        exit 1
    fi

    local success
    success=$(echo "$body" | jq -r '.success // false')

    if [[ "$success" != "true" ]]; then
        exit 1
    fi

    local account_count
    account_count=$(echo "$body" | jq -r '.result | length')

    case "$account_count" in
        0)
            exit 1
            ;;
        1)
            echo "$body" | jq -r '.result[0].id'
            ;;
        *)
            exit 1
            ;;
    esac
}

# Check if zone exists
check_existing_zone() {
    local response
    if ! response=$(api_request "GET" "/zones?name=${DOMAIN}" 2>/dev/null); then
        return 1
    fi

    local http_code="${response: -3}"
    local body="${response%???}"

    if [[ "$http_code" != "200" ]]; then
        return 1
    fi

    local success zone_count
    success=$(echo "$body" | jq -r '.success // false')
    zone_count=$(echo "$body" | jq -r '.result | length')

    if [[ "$success" == "true" && "$zone_count" -gt 0 ]]; then
        local zone_id zone_name nameservers
        zone_id=$(echo "$body" | jq -r '.result[0].id')
        zone_name=$(echo "$body" | jq -r '.result[0].name')
        nameservers=$(echo "$body" | jq -r '.result[0].name_servers[]' | tr '\n' ' ')

        echo "$zone_id"
        echo "$zone_name"
        echo "$nameservers"
        exit 0
    fi

    return 1
}

# Create new zone
create_zone() {
    local account_id="$1"

    local payload
    payload=$(jq -n \
        --arg name "$DOMAIN" \
        --arg account_id "$account_id" \
        '{
            name: $name,
            account: {
                id: $account_id
            },
            jump_start: false,
            type: "full"
        }')

    local response
    if ! response=$(api_request "POST" "/zones" "$payload" 2>/dev/null); then
        exit 1
    fi

    local http_code="${response: -3}"
    local body="${response%???}"

    if [[ "$http_code" != "200" ]]; then
        exit 1
    fi

    local success
    success=$(echo "$body" | jq -r '.success // false')

    if [[ "$success" != "true" ]]; then
        exit 1
    fi

    local zone_id zone_name nameservers
    zone_id=$(echo "$body" | jq -r '.result.id')
    zone_name=$(echo "$body" | jq -r '.result.name')
    nameservers=$(echo "$body" | jq -r '.result.name_servers[]' | tr '\n' ' ')

    echo "$zone_id"
    echo "$zone_name"
    echo "$nameservers"
}



# Main execution
main() {
    # Check for existing zone first
    if check_existing_zone; then
    echo "Zone already exists."
        return
    fi

    # Get account ID and create zone
    local account_id
    account_id=$(get_account_id)
    create_zone "$account_id"
}

main