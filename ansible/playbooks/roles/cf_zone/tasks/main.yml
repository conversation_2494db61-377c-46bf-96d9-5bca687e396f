---
- name: Add domain to Cloudflare
  ansible.builtin.script: |
    {{ role_path }}/files/add_zone.sh "{{ domain_name }}" "{{ cloudflare_token }}" {{ cloudflare_account_id | default('') }}
  register: cf_zone_raw
  changed_when: true
  failed_when: cf_zone_raw.rc != 0

- name: Parse Cloudflare zone creation output
  ansible.builtin.set_fact:
    cf_zone:
      zone_id: '{{ cf_zone_raw.stdout_lines[0] }}'
      zone_name: '{{ cf_zone_raw.stdout_lines[1] }}'
      name_servers: "{{ cf_zone_raw.stdout_lines[2].split(' ') | select() | list }}"
      success: true
      changed: '{{ cf_zone_raw.changed }}'

- name: Set fact for Cloudflare nameservers (backward compatibility)
  ansible.builtin.set_fact:
    cloudflare_nameservers: '{{ cf_zone.name_servers }}'

- name: Show Cloudflare zone information
  ansible.builtin.debug:
    msg: |
      Zone created/found successfully:
      - Zone ID: {{ cf_zone.zone_id }}
      - Zone Name: {{ cf_zone.zone_name }}
      - Nameservers: {{ cf_zone.name_servers | join(', ') }}

- name: Save zone information to facts file for API access
  ansible.builtin.copy:
    content: |
      {
        "domain": "{{ domain_name }}",
        "zone_id": "{{ cf_zone.zone_id }}",
        "zone_name": "{{ cf_zone.zone_name }}",
        "name_servers": {{ cf_zone.name_servers | to_json }},
        "success": {{ cf_zone.success | to_json }},
        "changed": {{ cf_zone.changed | to_json }},
      }
    dest: "/tmp/{{ domain_name | replace('.', '_') }}.json"
    mode: '0644'
    # remote_src: true
