---
- name: Set fact for site_root_dir
  ansible.builtin.set_fact:
    site_root_dir: '/home/<USER>/sites/{{ domain_name }}'

- name: Create site directories
  ansible.builtin.file:
    path: '{{ item }}'
    state: directory
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '0755'
  loop:
    - '{{ site_root_dir }}'
    - '{{ site_root_dir }}/public'
    - '{{ site_root_dir }}/backups'
    - '{{ site_root_dir }}/logs'

- name: Create database for cloned site
  community.mysql.mysql_db:
    name: '{{ wp_db_name }}'
    state: present
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci
  become: true

- name: Create database user for cloned site
  community.mysql.mysql_user:
    name: '{{ wp_db_user }}'
    password: '{{ wp_db_password }}'
    priv: '{{ wp_db_name }}.*:ALL'
    host: localhost
    state: present
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true

- name: Copy WordPress files from backup
  ansible.builtin.copy:
    src: '{{ wp_backup_files_path }}/'
    dest: '{{ site_root_dir }}/public/'
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: preserve
    remote_src: false
  when: wp_backup_files_path is defined

- name: Import SQL backup
  community.mysql.mysql_db:
    name: '{{ wp_db_name }}'
    state: import
    target: '{{ wp_backup_sql_path }}'
    login_user: root
    login_password: '{{ mysql_root_password }}'
    login_unix_socket: '{{ mysql_unix_socket_path }}'
  become: true
  when: wp_backup_sql_path is defined

- name: Update wp-config.php database settings
  ansible.builtin.replace:
    path: '{{ site_root_dir }}/public/wp-config.php'
    regexp: "define\\('{{ item.key }}'.*"
    replace: "define('{{ item.key }}', '{{ item.value }}');"
  loop:
    - { key: 'DB_NAME', value: '{{ wp_db_name }}' }
    - { key: 'DB_USER', value: '{{ wp_db_user }}' }
    - { key: 'DB_PASSWORD', value: '{{ wp_db_password }}' }
    - { key: 'DB_HOST', value: 'localhost' }

- name: Update site URLs in database
  ansible.builtin.command:
    cmd: /usr/local/bin/wp option update {{ item.option }} 'https://{{ domain_name }}' --path={{ site_root_dir }}/public
  loop:
    - { option: 'home' }
    - { option: 'siteurl' }
  changed_when: true

- name: Search and replace old domain in database
  ansible.builtin.command:
    cmd: /usr/local/bin/wp search-replace '{{ old_domain_name }}' '{{ domain_name }}' --path={{ site_root_dir }}/public
  when: old_domain_name is defined
  changed_when: true

- name: Set proper file permissions
  ansible.builtin.file:
    path: '{{ item.path }}'
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '{{ item.mode }}'
    recurse: '{{ item.recurse | default(false) }}'
  loop:
    - { path: '{{ site_root_dir }}/public', mode: '0755', recurse: true }
    - { path: '{{ site_root_dir }}/public/wp-content', mode: '0775' }
    - { path: '{{ site_root_dir }}/public/wp-config.php', mode: '0640' }
