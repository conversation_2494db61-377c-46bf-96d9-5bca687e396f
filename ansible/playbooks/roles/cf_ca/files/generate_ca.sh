#!/bin/bash

# Cloudflare Origin CA Certificate Generator
# Usage: ./generate_origin_ca.sh <domain> <api_token> <output_dir>

set -euo pipefail

DOMAIN="$1"
API_TOKEN="$2"
OUTPUT_DIR="$3"

if [[ -z "$DOMAIN" || -z "$API_TOKEN" || -z "$OUTPUT_DIR" ]]; then
    echo "Usage: $0 <domain> <api_token> <output_dir>" >&2
    exit 1
fi

API_URL="https://api.cloudflare.com/client/v4/certificates"

mkdir -p "$OUTPUT_DIR"

KEY_FILE="$OUTPUT_DIR/key.pem"
CSR_FILE="$OUTPUT_DIR/crs.pem"
CRT_FILE="$OUTPUT_DIR/crt.pem"

# Generate ECC private key
if [[ ! -f "$KEY_FILE" ]]; then
    openssl ecparam -genkey -name prime256v1 -noout -out "$KEY_FILE"
    chmod 600 "$KEY_FILE"
fi

# Generate CSR
if [[ ! -f "$CSR_FILE" ]] || [[ "$KEY_FILE" -nt "$CSR_FILE" ]]; then
    CSR_CONFIG=$(mktemp)
    cat > "$CSR_CONFIG" << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
CN = $DOMAIN

[v3_req]
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = *.$DOMAIN
EOF

    openssl req -new -key "$KEY_FILE" -out "$CSR_FILE" -config "$CSR_CONFIG"
    rm "$CSR_CONFIG"
fi

# Request certificate
if [[ ! -f "$CRT_FILE" ]] || [[ "$CSR_FILE" -nt "$CRT_FILE" ]]; then
    CSR_CONTENT=$(cat "$CSR_FILE")

    PAYLOAD=$(jq -n \
        --arg csr "$CSR_CONTENT" \
        --argjson hostnames "[\"$DOMAIN\", \"*.$DOMAIN\"]" \
        '{
            request_type: "origin-ecc",
            hostnames: $hostnames,
            requested_validity: 5475,
            csr: $csr
        }')

    RESPONSE=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $API_TOKEN" \
        --data "$PAYLOAD")

    SUCCESS=$(echo "$RESPONSE" | jq -r '.success')
    if [[ "$SUCCESS" != "true" ]]; then
        echo "$RESPONSE" | jq -r '.errors[]? | "\(.code): \(.message)"' >&2
        exit 1
    fi

    CERT=$(echo "$RESPONSE" | jq -r '.result.certificate')
    echo "$CERT" > "$CRT_FILE"
    # chmod 644 "$CRT_FILE"
fi

echo "$KEY_FILE"
echo "$CRT_FILE"