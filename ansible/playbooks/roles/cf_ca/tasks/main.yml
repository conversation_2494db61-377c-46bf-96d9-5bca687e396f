---
- name: Ensure cert directory exists
  ansible.builtin.file:
    path: '/etc/cf/{{ domain_name }}'
    state: directory
    mode: '0755'
    owner: root
    group: root
  become: true

- name: Generate Cloudflare Origin CA certificate and key
  ansible.builtin.script: |
    {{ role_path }}/files/generate_ca.sh {{ domain_name }} {{ cloudflare_token }} /etc/cf/{{ domain_name }}
  args:
    creates: '/etc/cf/{{ domain_name }}/crt.pem'
  environment:
    PATH: '/usr/local/bin:/usr/bin:/bin'
  register: ca_script_result
  changed_when: ca_script_result.rc == 0
  become: true
  notify: Set certificate and key file permissions
