---
- name: UFW policy
  community.general.ufw:
    direction: '{{ item.direction }}'
    policy: '{{ item.policy }}'
  loop:
    - { direction: 'incoming', policy: 'deny' }
    - { direction: 'outgoing', policy: 'allow' }

- name: UFW ips
  ansible.builtin.uri:
    url: https://www.cloudflare.com/ips-v4
    return_content: true
    method: GET
    validate_certs: true
  register: cloudflare_ips_response
  run_once: true
  # delegate_to: localhost

- name: UFW ips facts
  ansible.builtin.set_fact:
    cloudflare_ipv4_cidrs: "{{ cloudflare_ips_response.content.splitlines() | map('trim') | list }}"
  run_once: true
  # delegate_to: localhost

- name: UFW allow ssh
  community.general.ufw:
    rule: allow
    port: '{{ ssh_port | default(22) }}'
    proto: tcp
    comment: 'Allow SSH on port {{ ssh_port }}'

- name: UFW allow mosh
  community.general.ufw:
    rule: allow
    port: '60000:60002'
    proto: udp
    comment: 'Allow Mosh on ports 60000-60002'

- name: UFW allow http
  community.general.ufw:
    rule: allow
    port: '80'
    proto: tcp
    from: '{{ item }}'
    comment: 'Allow Cloudflare IPv4 for HTTP'
  loop: '{{ cloudflare_ipv4_cidrs }}'

- name: UFW allow https
  community.general.ufw:
    rule: allow
    port: '443'
    proto: tcp
    from: '{{ item }}'
    comment: 'Allow Cloudflare IPv4 for HTTPS'
  loop: '{{ cloudflare_ipv4_cidrs }}'

- name: UFW enable
  community.general.ufw:
    state: enabled
    # The 'state: enabled' command confirms rules and starts the firewall.
    # It will prompt if run from SSH on non-standard port, but Ansible handles this.
