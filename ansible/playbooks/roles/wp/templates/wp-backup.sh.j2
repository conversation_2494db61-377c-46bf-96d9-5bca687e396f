#!/bin/bash
#
# WordPress Backup Script for {{ domain_name }}
# Generated by Ansible
#

set -euo pipefail

# Configuration
SITE_ROOT="{{ site_root_dir }}"
DOMAIN="{{ domain_name }}"
DB_NAME="{{ wp_db_name }}"
DB_USER="{{ wp_db_user }}"
DB_PASSWORD="{{ wp_db_password }}"
BACKUP_DIR="${SITE_ROOT}/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="${DOMAIN}_${DATE}"

# Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a "${SITE_ROOT}/logs/backup.log"
}

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

log "Starting backup for ${DOMAIN}"

# Database backup
log "Backing up database: ${DB_NAME}"
mysqldump -u "${DB_USER}" -p"${DB_PASSWORD}" "${DB_NAME}" > "${BACKUP_DIR}/${BACKUP_NAME}_database.sql"

# Files backup
log "Backing up WordPress files"
tar -czf "${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz" -C "${SITE_ROOT}" public/

# Create combined backup
log "Creating combined backup archive"
tar -czf "${BACKUP_DIR}/${BACKUP_NAME}_complete.tar.gz" -C "${BACKUP_DIR}" "${BACKUP_NAME}_database.sql" "${BACKUP_NAME}_files.tar.gz"

# Cleanup individual files
rm "${BACKUP_DIR}/${BACKUP_NAME}_database.sql" "${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz"

# Set permissions
chmod 600 "${BACKUP_DIR}/${BACKUP_NAME}_complete.tar.gz"

log "Backup completed: ${BACKUP_NAME}_complete.tar.gz"

# Optional: Upload to remote storage (uncomment and configure as needed)
# rsync -av "${BACKUP_DIR}/${BACKUP_NAME}_complete.tar.gz" user@remote-server:/backups/
