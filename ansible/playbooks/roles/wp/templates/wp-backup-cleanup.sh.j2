#!/bin/bash
#
# WordPress Backup Cleanup Script for {{ domain_name }}
# Generated by Ansible
# Removes backups older than {{ wp_backup_retention_days | default(30) }} days
#

set -euo pipefail

# Configuration
BACKUP_DIR="{{ site_root_dir }}/backups"
RETENTION_DAYS={{ wp_backup_retention_days | default(30) }}
DOMAIN="{{ domain_name }}"

# Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a "${BACKUP_DIR}/cleanup.log"
}

# Check if backup directory exists
if [[ ! -d "${BACKUP_DIR}" ]]; then
    log "Backup directory does not exist: ${BACKUP_DIR}"
    exit 1
fi

log "Starting cleanup for ${DOMAIN} - removing backups older than ${RETENTION_DAYS} days"

# Count files before cleanup
BEFORE_COUNT=$(find "${BACKUP_DIR}" -name "${DOMAIN}_*_complete.tar.gz" -type f | wc -l)
log "Found ${BEFORE_COUNT} backup files"

# Remove old backups
DELETED_COUNT=$(find "${BACKUP_DIR}" -name "${DOMAIN}_*_complete.tar.gz" -type f -mtime +${RETENTION_DAYS} -delete -print | wc -l)

# Count files after cleanup
AFTER_COUNT=$(find "${BACKUP_DIR}" -name "${DOMAIN}_*_complete.tar.gz" -type f | wc -l)

log "Cleanup completed: deleted ${DELETED_COUNT} files, ${AFTER_COUNT} files remaining"

# Clean up old log files (keep last 10)
find "${BACKUP_DIR}" -name "*.log" -type f | sort | head -n -10 | xargs -r rm

log "Log cleanup completed"
