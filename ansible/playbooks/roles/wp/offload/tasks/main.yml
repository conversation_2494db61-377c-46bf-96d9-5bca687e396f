---
- name: Create backup archive of site files
  community.general.archive:
    path:
      - '{{ site_root_dir }}/public'
      - '{{ site_root_dir }}/cache'
      - '{{ site_root_dir }}/logs'
      - '{{ site_root_dir }}/backups'
    dest: '/tmp/{{ domain_name }}-site-backup.tar.gz'
    format: gz
    mode: '0644'
  become: true

- name: Upload backup to Google Drive (custom script)
  ansible.builtin.command:
    cmd: '/usr/local/bin/gdrive upload /tmp/{{ domain_name }}-site-backup.tar.gz --parent {{ gdrive_folder_id }}'
  register: gdrive_upload
  changed_when: gdrive_upload.rc == 0
  failed_when: gdrive_upload.rc != 0

- name: Remove local backup archive
  ansible.builtin.file:
    path: '/tmp/{{ domain_name }}-site-backup.tar.gz'
    state: absent
  become: true
