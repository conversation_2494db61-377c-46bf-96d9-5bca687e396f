---
- name: <PERSON><PERSON> Docker is installed and running
  ansible.builtin.systemd:
    name: docker
    state: started
    enabled: true
  become: true

- name: Ensure Docker Compose is installed
  ansible.builtin.package:
    name: docker-compose-plugin
    state: present
  become: true

- name: Set site root directory path
  ansible.builtin.set_fact:
    site_root_dir: '/home/<USER>/sites/{{ domain_name }}'

- name: Check if site root directory exists
  ansible.builtin.stat:
    path: '{{ site_root_dir }}'
  register: site_root_exists

- name: Setting up root dir for {{ domain_name }}
  when: not site_root_exists.stat.exists
  ansible.builtin.file:
    path: '{{ item }}'
    state: directory
    owner: '{{ user }}'
    group: '{{ user }}'
    mode: '0755'
  loop:
    - '{{ site_root_dir }}'
    - '{{ site_root_dir }}/cache'
    - '{{ site_root_dir }}/logs'
    - '{{ site_root_dir }}/data'
    - '{{ site_root_dir }}/backups'

- name: Copy .env
  ansible.builtin.template:
    src: '/home/<USER>/workspace/deploysite/ansible/sites/{{ domain_name }}/.env'
    dest: '{{ site_root_dir }}/.env'
    mode: '0644'
    owner: '{{ user }}'
    group: '{{ user }}'

- name: Download compose.yml from GitHub or provided URL
  ansible.builtin.get_url:
    url: '{{ compose_url }}'
    dest: '{{ site_root_dir }}/compose.yml'
    mode: '0644'
    owner: '{{ user }}'
    group: '{{ user }}'
  when: compose_url is defined and compose_url | length > 0

# Copy local compose.yml if no URL provided
- name: Copy local compose.yml if no URL provided
  ansible.builtin.template:
    src: '/home/<USER>/workspace/deploysite/ansible/sites/{{ domain_name }}/compose.yml'
    dest: '{{ site_root_dir }}/compose.yml'
    mode: '0644'
    owner: '{{ user }}'
    group: '{{ user }}'
  when: compose_url is not defined or compose_url | length == 0

- name: Pull Docker images
  community.docker.docker_compose_v2:
    project_src: '{{ site_root_dir }}'
    pull: always
  when: container_force_pull | default(false)

- name: Stop and remove existing containers (if recreating)
  community.docker.docker_compose_v2:
    project_src: '{{ site_root_dir }}'
    state: absent
  when: container_recreate | default(false)

- name: Deploy container service with Docker Compose
  community.docker.docker_compose_v2:
    project_src: '{{ site_root_dir }}'
    state: present

- name: Wait for container to be healthy
  community.docker.docker_container_info:
    name: '{{ container_name }}'
  register: container_info
  until: container_info.container.State.Status == "running"
  retries: 10
  delay: 5
  when: container_wait_healthy | default(true)

- name: Display container status
  ansible.builtin.debug:
    msg: 'Container {{ container_name }} is {{ container_info.container.State.Status }}'
  when: container_info is defined and container_info.container is defined

- name: Check if Nginx configuration exists
  ansible.builtin.stat:
    path: '/etc/nginx/sites-available/{{ domain_name }}'
  register: nginx_site_config_check
  become: true

- name: Create Nginx configuration for {{ domain_name }}
  ansible.builtin.copy:
    src: '/etc/nginx/sites-available/container.conf'
    dest: '/etc/nginx/sites-available/{{ domain_name }}'
    owner: root
    group: root
    mode: '0644'
    backup: true
    remote_src: true
  when: not nginx_site_config_check.stat.exists
  register: nginx_config_created
  become: true
  notify: Reload Nginx

- name: Replace placeholders in Nginx config
  ansible.builtin.replace:
    path: '/etc/nginx/sites-available/{{ domain_name }}'
    regexp: '{{ item.regexp }}'
    replace: '{{ item.replace }}'
  loop:
    - { regexp: '\[\[DOMAIN\]\]', replace: '{{ domain_name }}' }
    - { regexp: '\[\[USER\]\]', replace: '{{ user }}' }
    - { regexp: '\[\[PORT\]\]', replace: '{{ container_port }}' }
  when: not nginx_site_config_check.stat.exists
  become: true
  notify: Reload Nginx

- name: Check if Nginx site is already enabled
  ansible.builtin.stat:
    path: '/etc/nginx/sites-enabled/{{ domain_name }}'
  register: nginx_site_enabled_check
  become: true

- name: Enable Nginx site
  ansible.builtin.file:
    src: '/etc/nginx/sites-available/{{ domain_name }}'
    dest: '/etc/nginx/sites-enabled/{{ domain_name }}'
    state: link
  when: not nginx_site_enabled_check.stat.exists
  become: true
  notify: Reload Nginx

- name: Test Nginx configuration
  ansible.builtin.command:
    cmd: nginx -t
  become: true
  changed_when: false
