# This file is managed by <PERSON><PERSON>. Do not edit directly.

[DEFAULT]
# Ban hosts for one hour:
bantime = 3600

# A host is banned after 5 attempts:
maxretry = 5

# Find logs in /var/log/
logtarget = SYSLOG

# Override /etc/fail2ban/jail.d/defaults-debian.conf
ignoreip = 127.0.0.1/8 ::1

# Your SSH jail (sshd) should typically be managed by the deploy user
# accessing on the new port. This playbook disables SSH password auth anyway.
# We'll disable the default SSHD jail here and rely on custom ones.
[sshd]
enabled = true

# Nginx Specific Jails (assuming Nginx logs are standard /var/log/nginx/*access.log)
# Ensure your Nginx configuration generates these logs.

# Ban IPs that request non-existent pages too many times (e.g., scanners)
[nginx-http-auth]
enabled = true
port = http,https
filter = nginx-http-auth
logpath = /var/log/nginx/*access.log
maxretry = 5
bantime = 600

# Ban IPs trying to access specific common bad paths
[nginx-bad-bots]
enabled = true
port = http,https
filter = nginx-bad-bots
logpath = /var/log/nginx/*access.log
maxretry = 3
bantime = 86400 # Ban for 24 hours

# You might want to create custom filters for nginx-http-auth and nginx-bad-bots
# in /etc/fail2ban/filter.d/ if the default ones aren't sufficient or don't exist.
# Common filters:
# /etc/fail2ban/filter.d/nginx-http-auth.conf
# /etc/fail2ban/filter.d/nginx-bad-bots.conf
