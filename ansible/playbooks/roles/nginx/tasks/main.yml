---
- name: Ensure Nginx service is running and enabled
  ansible.builtin.systemd:
    name: nginx
    state: started
    enabled: true

- name: Copy nginx files to /etc/nginx
  ansible.builtin.copy:
    src: '/home/<USER>/workspace/deploysite/ansible/files/nginx/'
    dest: '/etc/nginx/'
    owner: root
    group: root
    mode: '0644'
    # remote_src: true
  notify: Reload Nginx

- name: Replace user placeholder
  ansible.builtin.replace:
    path: '/etc/nginx/nginx.conf'
    regexp: '\[\[USER\]\]'
    replace: '{{ user }}'

- name: Symlink default
  ansible.builtin.file:
    src: '/etc/nginx/sites-available/default'
    dest: '/etc/nginx/sites-enabled/default'
    state: link
    owner: root
    group: root
  notify: Reload Nginx
