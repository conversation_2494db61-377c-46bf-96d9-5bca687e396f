---
- name: Update apt cache
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 3600

- name: Install common prerequisites
  ansible.builtin.apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - gpg
      - gnupg
      - software-properties-common
      - lsb-release
      - debian-keyring
      - python3-pymysql
      - vim
      - ufw
      - fail2ban
      - mosh
      - git
      - mariadb-server
      - nginx
      - jq
      # - certbot
      # - python3-certbot-nginx
    state: present

- name: Download WP-CLI
  ansible.builtin.get_url:
    url: https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar
    dest: /usr/local/bin/wp
    mode: '0755'
    owner: root
    group: root
