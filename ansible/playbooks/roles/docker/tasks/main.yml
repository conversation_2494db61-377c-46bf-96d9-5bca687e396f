---
- name: <PERSON>d Docker GPG key
  ansible.builtin.apt_key:
    url: https://download.docker.com/linux/debian/gpg
    state: present
    keyring: /usr/share/keyrings/docker-archive-keyring.gpg

- name: Add Docker APT repository
  ansible.builtin.apt_repository:
    repo: deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian bookworm stable
    state: present
    filename: docker

- name: Install Docker Engine
  ansible.builtin.apt:
    name:
      - docker-ce
      - docker-ce-cli
      - containerd.io
      - docker-buildx-plugin
      - docker-compose-plugin
    state: present
    update_cache: true

- name: Add user to docker group
  ansible.builtin.user:
    name: '{{ user }}'
    groups: docker
    append: true

- name: Copy/create Docker daemon.json
  ansible.builtin.template:
    src: daemon.json.j2
    dest: /etc/docker/daemon.json
    owner: root
    group: root
    mode: '0644'
  notify: <PERSON><PERSON> Docker

- name: Ensure Docker service is running and enabled
  ansible.builtin.systemd:
    name: docker
    state: started
    enabled: true
