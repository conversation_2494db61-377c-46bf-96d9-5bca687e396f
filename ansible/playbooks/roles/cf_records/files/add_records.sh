#!/bin/bash

# Cloudflare DNS Record Creator
# Usage: ./add_records.sh <zone_id> <api_token> <domain> <ip>

set -euo pipefail

ZONE_ID="$1"
API_TOKEN="$2"
DOMAIN="$3"
IP="$4"

if [[ -z "$ZONE_ID" || -z "$API_TOKEN" || -z "$DOMAIN" || -z "$IP" ]]; then
    echo "Usage: $0 <zone_id> <api_token> <domain> <ip>" >&2
    exit 1
fi

API_URL="https://api.cloudflare.com/client/v4/zones/$ZONE_ID/dns_records"

create_record() {
    local type="$1"
    local name="$2"
    local content="$3"
    local comment="$4"
    local payload
    payload=$(jq -n \
        --arg type "$type" \
        --arg name "$name" \
        --arg content "$content" \
        --arg comment "$comment" \
        '{
            type: $type,
            name: $name,
            content: $content,
            ttl: 3600,
            proxied: true,
            comment: $comment
        }')

    response=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $API_TOKEN" \
        --data "$payload")

    success=$(echo "$response" | jq -r '.success')
    if [[ "$success" != "true" ]]; then
        echo "$response" | jq -r '.errors[]? | "CF API ERROR: [\(.code)] \(.message)"' >&2
        return 1
    fi
    echo "$response" | jq -r '.result.id'
}

# Add A record for root domain
create_record "A" "$DOMAIN" "$IP" "Root domain A record"

# Add CNAME record for wildcard
create_record "CNAME" "*.$DOMAIN" "$DOMAIN" "Wildcard CNAME record"
