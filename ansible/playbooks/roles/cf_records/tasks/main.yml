---
- name: Add A and CNAME records to Cloudflare via API
  ansible.builtin.script: |
    {{ role_path }}/files/add_records.sh "{{ cloudflare_zone_id }}" "{{ cloudflare_token }}" "{{ domain_name }}" "{{ server_ip }}"
  environment:
    PATH: '/usr/local/bin:/usr/bin:/bin'
  register: cf_records_raw
  changed_when: true
  failed_when: cf_records_raw.rc != 0

- name: Set API response fact
  ansible.builtin.set_fact:
    cloudflare_api_response: '{{ cf_records_raw.stdout }}'

- name: Parse Cloudflare API response
  ansible.builtin.set_fact:
    cf_records:
      success: true
      changed: '{{ cf_records_raw.changed }}'
      records: '{{ cf_records_raw.stdout_lines }}'
  when: cf_records_raw.stdout_lines | length > 0

- name: Show Cloudflare records creation output
  ansible.builtin.debug:
    msg: |
      Cloudflare records created successfully:
      - Records: {{ cf_records.records | join(', ') }}
      - Success: {{ cf_records.success }}
      - Changed: {{ cf_records.changed }}

- name: Save records information to facts file for API access
  ansible.builtin.copy:
    content: |
      {
        "domain": "{{ domain_name }}",
        "records": {{ cf_records.records | to_json }},
        "success": {{ cf_records.success | to_json }},
        "changed": {{ cf_records.changed | to_json }}
      }
    dest: "/tmp/{{ domain_name | replace('.', '_') }}_records.json"
    mode: '0644'
    # remote_src: true
  when: cf_records.success | bool
