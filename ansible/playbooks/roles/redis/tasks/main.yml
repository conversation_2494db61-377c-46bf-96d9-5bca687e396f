---
- name: Gather facts if not already collected
  ansible.builtin.setup:
  when: ansible_distribution_release is not defined

- name: Add Redis GPG key
  ansible.builtin.apt_key:
    url: https://packages.redis.io/gpg
    state: present
    keyring: /usr/share/keyrings/redis-archive-keyring.gpg

- name: Add Redis repository
  ansible.builtin.apt_repository:
    repo: 'deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb {{ ansible_distribution_release }} main'
    state: present
    filename: redis

- name: Update package cache
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: 3600

- name: Install Redis
  ansible.builtin.apt:
    name: redis
    state: present

- name: Start and enable Redis service
  ansible.builtin.systemd:
    name: redis-server
    state: started
    enabled: true

- name: Ensure Redis is running
  ansible.builtin.systemd:
    name: redis-server
    state: started
