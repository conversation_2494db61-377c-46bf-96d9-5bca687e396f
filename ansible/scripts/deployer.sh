#!/bin/bash

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
LOG_DIR="$SCRIPT_DIR/../logs"
LOG_FILE="$LOG_DIR/deploy-$(date '+%Y%m%d').log"
LOCK_FILE="/tmp/deploy.lock"
mkdir -p "$LOG_DIR"

# Locking to prevent concurrent runs
exec 9>"$LOCK_FILE"
flock -n 9 || { echo "Another deployment is in progress. Exiting."; exit 1; }

# Color definitions
COLOR_GREEN="\e[32m"
COLOR_YELLOW="\e[33m"
COLOR_RED="\e[31m"
COLOR_BLUE="\e[34m"
COLOR_RESET="\e[0m"

_timestamp() { date '+%Y-%m-%d %H:%M:%S'; }

info()    { echo -e "${COLOR_BLUE}[INFO] $( _timestamp ) $*${COLOR_RESET}"    | tee -a "$LOG_FILE"; }
warn()    { echo -e "${COLOR_YELLOW}[WARN] $( _timestamp ) $*${COLOR_RESET}"   | tee -a "$LOG_FILE"; }
error()   { echo -e "${COLOR_RED}[ERROR] $( _timestamp ) $*${COLOR_RESET}"     | tee -a "$LOG_FILE" >&2; }
success() { echo -e "${COLOR_GREEN}[DONE] $( _timestamp ) $*${COLOR_RESET}"     | tee -a "$LOG_FILE"; }
fail()    { error "$*"; exit 1; }

: "${INVENTORY_FILE:=$SCRIPT_DIR/../hosts.ini}"

run_playbook() {
  local playbook=$1; shift
  local playbook_path="$SCRIPT_DIR/../playbooks/$playbook"
  [[ -f "$playbook_path" ]] || fail "$playbook_path not found."
  ansible-playbook "$playbook_path" -i "$INVENTORY_FILE" $ANSIBLE_OPTIONS "$@" | tee -a "$LOG_FILE"
}

run_playbook_safe() {
  local playbook=$1; shift
  info "Running $playbook..."
  if run_playbook "$playbook" "$@"; then
    success "$playbook completed."
  else
    fail "$playbook failed."
  fi
}

lint_file() {
  local config_file=$1
  if command -v ansible-lint >/dev/null 2>&1; then
    info "Linting $config_file with ansible-lint..."
    ansible-lint "$config_file" && success "Lint passed for $config_file"
  else
    warn "ansible-lint not found. Skipping lint check."
  fi
}

export ANSIBLE_FORCE_COLOR=true
export PY_COLORS=1

HOST_LIMIT=""
EXTRA_VARS=""

RUN_SETUP=false
RUN_STACK=false

RUN_CF_CA=false
RUN_CF_RECORDS=false
RUN_CF_ZONE=false

RUN_WP_INSTALL=false
RUN_WP_UNINSTALL=false
RUN_WP_CLONE=false

RUN_CONTAINER_ENABLE=false
RUN_CONTAINER_DISABLE=false

ANSIBLE_OPTIONS=""

while [[ $# -gt 0 ]]; do
  case $1 in
    --setup) RUN_SETUP=true; shift;;
    --stack) RUN_STACK=true; shift;;

    --ca) RUN_CF_CA=true; shift;;
    --records) RUN_CF_RECORDS=true; shift;;
    --zone) RUN_CF_ZONE=true; shift;;

    --wp-install) RUN_WP_INSTALL=true; shift;;
    --wp-uninstall) RUN_WP_UNINSTALL=true; shift;;
    --wp-clone) RUN_WP_CLONE=true; shift;;

    --container-enable) RUN_CONTAINER_ENABLE=true; shift;;
    --container-disable) RUN_CONTAINER_DISABLE=true; shift;;

    --server) RUN_SETUP=true; RUN_STACK=true; shift;;
    -l) HOST_LIMIT="-l $2"; shift 2;;
    *.yml) [[ -r $1 ]] || fail "Cannot read config file: $1"; EXTRA_VARS="-e @$1"; lint_file "$1"; shift;;
    *)
      if [[ -z "$HOST_LIMIT" ]]; then
        HOST_LIMIT="-l $1"
        shift
      else
        fail "Unknown argument: $1"
      fi
      ;;
  esac
done

$RUN_SETUP && run_playbook_safe setup.yml $HOST_LIMIT $EXTRA_VARS
$RUN_STACK && run_playbook_safe stack.yml $HOST_LIMIT $EXTRA_VARS

$RUN_CF_CA && run_playbook_safe cf_ca.yml $HOST_LIMIT $EXTRA_VARS
$RUN_CF_RECORDS && run_playbook_safe cf_records.yml $HOST_LIMIT $EXTRA_VARS
$RUN_CF_ZONE && run_playbook_safe cf_zone.yml $HOST_LIMIT $EXTRA_VARS

$RUN_WP_INSTALL && run_playbook_safe wp_install.yml $HOST_LIMIT $EXTRA_VARS
$RUN_WP_UNINSTALL && run_playbook_safe wp_uninstall.yml $HOST_LIMIT $EXTRA_VARS
$RUN_WP_CLONE && run_playbook_safe wp_clone.yml $HOST_LIMIT $EXTRA_VARS

$RUN_CONTAINER_DISABLE && run_playbook_safe container_disable.yml $HOST_LIMIT $EXTRA_VARS
$RUN_CONTAINER_ENABLE && run_playbook_safe container_enable.yml $HOST_LIMIT $EXTRA_VARS

# Auto-delete logs older than 7 days
find "$LOG_DIR" -name 'deploy-*.log' -mtime +7 -exec rm {} \;

# Notifier (to be implemented) - preferably on Telegram
# [[ -n "$TELEGRAM_BOT" ]] && curl -X POST -H "Content-Type: application/json" -d '{"text":"Deployment successful"}' "$TELEGRAM_BOT"
