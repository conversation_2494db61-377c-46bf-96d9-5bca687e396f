#!/bin/bash

set -euo pipefail

SITES_DIR="$(dirname "$0")/../sites"
DEPLOYER_SCRIPT="$(dirname "$0")/deployer.sh"

for site in "$SITES_DIR"/*; do
  VARS_FILE="$site/vars.yml"
  if [[ -f "$VARS_FILE" ]]; then
    # Extract 'type' from vars.yml
    TYPE=$(grep '^type:' "$VARS_FILE" | awk '{print $2}' | tr -d '"')
    if [[ "$TYPE" == "wp" ]]; then
      echo "Deploying WordPress site: $site"
      bash "$DEPLOYER_SCRIPT" --wp-install "$VARS_FILE"
    elif [[ "$TYPE" == "container" ]]; then
      echo "Deploying container site: $site"
      bash "$DEPLOYER_SCRIPT" --container-enable "$VARS_FILE"
    else
      echo "Unknown type '$TYPE' for $site, skipping."
    fi
  else
    echo "No vars.yml found in $site, skipping."
  fi
done
